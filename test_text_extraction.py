#!/usr/bin/env python3
"""
测试文本提取功能
"""

import os
import asyncio
from douyin_mcp_server.server import DouyinProcessor

# 设置API密钥
os.environ['DASHSCOPE_API_KEY'] = 'sk-d4d314c9749c474d95efbb2e85b0769b'

async def test_text_extraction():
    """测试文本提取"""
    share_link = "https://v.douyin.com/VNWSjQzeseU/"
    
    print("🧪 测试抖音视频文本提取")
    print("=" * 50)
    print(f"🔗 测试链接: {share_link}")
    
    try:
        processor = DouyinProcessor(os.getenv('DASHSCOPE_API_KEY'))
        
        # 1. 解析视频信息
        print("\n📱 步骤1: 解析视频信息...")
        video_info = processor.parse_share_url(share_link)
        print(f"✅ 视频ID: {video_info['video_id']}")
        print(f"✅ 标题: {video_info['title']}")
        print(f"✅ 视频URL: {video_info['url'][:50]}...")
        
        # 2. 提取文本内容
        print("\n🎧 步骤2: 提取文本内容...")
        print("⏳ 正在调用阿里云百炼API进行语音识别...")
        print("⏳ 这可能需要几分钟时间，请耐心等待...")
        
        text_content = processor.extract_text_from_video_url(video_info['url'])
        
        print("\n✅ 文本提取成功！")
        print("📝 提取的文本内容:")
        print("-" * 60)
        print(text_content)
        print("-" * 60)
        
        # 3. 保存结果
        with open('extracted_text.txt', 'w', encoding='utf-8') as f:
            f.write(f"视频标题: {video_info['title']}\n")
            f.write(f"视频ID: {video_info['video_id']}\n")
            f.write(f"提取时间: {__import__('datetime').datetime.now()}\n")
            f.write("-" * 60 + "\n")
            f.write(text_content)
        
        print(f"\n💾 结果已保存到 extracted_text.txt")
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_text_extraction())
