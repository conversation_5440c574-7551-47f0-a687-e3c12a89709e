#!/usr/bin/env python3
"""
抖音视频批量处理工具
支持批量提取文字、获取下载链接、解析视频信息
"""

import os
import asyncio
import json
import time
from datetime import datetime
from douyin_mcp_server.server import DouyinProcessor

# 设置API密钥
os.environ['DASHSCOPE_API_KEY'] = 'sk-d4d314c9749c474d95efbb2e85b0769b'

class BatchDouyinProcessor:
    def __init__(self):
        self.processor = DouyinProcessor(os.getenv('DASHSCOPE_API_KEY'))
        self.results = []
        self.success_count = 0
        self.error_count = 0
    
    def load_links_from_file(self, filename):
        """从文件加载链接"""
        links = []
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line and ('douyin.com' in line or 'v.douyin.com' in line):
                        links.append(line)
            print(f"📁 从文件 {filename} 加载了 {len(links)} 个链接")
            return links
        except FileNotFoundError:
            print(f"❌ 文件 {filename} 不存在")
            return []
    
    def extract_links_from_text(self, text):
        """从文本中提取所有抖音链接"""
        import re
        # 匹配抖音链接的正则表达式
        pattern = r'https?://[^\s]*(?:douyin\.com|v\.douyin\.com)[^\s]*'
        links = re.findall(pattern, text)
        return links
    
    async def process_single_link(self, link, index, total, function='text'):
        """处理单个链接"""
        print(f"\n🔄 处理第 {index}/{total} 个链接...")
        print(f"🔗 链接: {link[:50]}...")
        
        result = {
            'index': index,
            'link': link,
            'timestamp': datetime.now().isoformat(),
            'function': function
        }
        
        try:
            # 1. 解析视频信息
            video_info = self.processor.parse_share_url(link)
            result.update({
                'video_id': video_info['video_id'],
                'title': video_info['title'],
                'download_url': video_info['url']
            })
            
            # 2. 根据功能执行相应操作
            if function == 'text':
                print("🎧 正在提取文字...")
                text_content = self.processor.extract_text_from_video_url(video_info['url'])
                result['text_content'] = text_content
                print(f"✅ 文字提取成功: {text_content[:50]}...")
                
            elif function == 'download':
                result['status'] = 'success'
                print(f"✅ 下载链接获取成功")
                
            elif function == 'info':
                result['status'] = 'success'
                print(f"✅ 视频信息解析成功")
            
            result['status'] = 'success'
            self.success_count += 1
            
        except Exception as e:
            result['status'] = 'error'
            result['error'] = str(e)
            self.error_count += 1
            print(f"❌ 处理失败: {str(e)}")
        
        self.results.append(result)
        return result
    
    async def batch_process(self, links, function='text', delay=2):
        """批量处理链接"""
        print(f"🚀 开始批量处理 {len(links)} 个链接")
        print(f"🎯 功能: {function}")
        print(f"⏱️ 延时: {delay}秒")
        print("=" * 60)
        
        start_time = time.time()
        
        for i, link in enumerate(links, 1):
            await self.process_single_link(link, i, len(links), function)
            
            # 添加延时避免API限制
            if i < len(links):
                print(f"⏳ 等待 {delay} 秒...")
                await asyncio.sleep(delay)
        
        end_time = time.time()
        duration = end_time - start_time
        
        print("\n" + "=" * 60)
        print(f"🎉 批量处理完成！")
        print(f"📊 总计: {len(links)} 个链接")
        print(f"✅ 成功: {self.success_count} 个")
        print(f"❌ 失败: {self.error_count} 个")
        print(f"⏱️ 耗时: {duration:.1f} 秒")
        
        return self.results
    
    def save_results(self, filename=None):
        """保存结果到文件"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"batch_results_{timestamp}.json"
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(self.results, f, ensure_ascii=False, indent=2)
        
        print(f"💾 结果已保存到: {filename}")
        return filename
    
    def save_text_only(self, filename=None):
        """仅保存提取的文字内容"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"extracted_texts_{timestamp}.txt"
        
        with open(filename, 'w', encoding='utf-8') as f:
            for result in self.results:
                if result.get('status') == 'success' and 'text_content' in result:
                    f.write(f"标题: {result.get('title', '未知')}\n")
                    f.write(f"视频ID: {result.get('video_id', '未知')}\n")
                    f.write(f"文字内容: {result['text_content']}\n")
                    f.write("-" * 80 + "\n\n")
        
        print(f"📝 文字内容已保存到: {filename}")
        return filename

async def main():
    print("🎬 抖音批量处理工具")
    print("=" * 50)
    
    processor = BatchDouyinProcessor()
    
    print("选择输入方式:")
    print("1. 从文件读取链接")
    print("2. 手动输入链接")
    print("3. 粘贴包含链接的文本")
    
    choice = input("请选择 (1-3): ").strip()
    links = []
    
    if choice == "1":
        filename = input("请输入文件名 (默认: links.txt): ").strip() or "links.txt"
        links = processor.load_links_from_file(filename)
    
    elif choice == "2":
        print("请输入抖音链接，每行一个，输入空行结束:")
        while True:
            link = input().strip()
            if not link:
                break
            links.append(link)
    
    elif choice == "3":
        print("请粘贴包含抖音链接的文本:")
        text = input().strip()
        links = processor.extract_links_from_text(text)
        print(f"从文本中提取到 {len(links)} 个链接")
    
    if not links:
        print("❌ 没有找到有效链接")
        return
    
    print(f"\n找到 {len(links)} 个链接:")
    for i, link in enumerate(links[:5], 1):  # 只显示前5个
        print(f"{i}. {link[:50]}...")
    if len(links) > 5:
        print(f"... 还有 {len(links) - 5} 个链接")
    
    print("\n选择处理功能:")
    print("1. 提取文字内容 (需要API密钥)")
    print("2. 获取下载链接 (免费)")
    print("3. 获取视频信息 (免费)")
    
    func_choice = input("请选择 (1-3): ").strip()
    function_map = {"1": "text", "2": "download", "3": "info"}
    function = function_map.get(func_choice, "text")
    
    delay = float(input("请输入处理间隔秒数 (默认2秒，避免API限制): ").strip() or "2")
    
    # 开始批量处理
    results = await processor.batch_process(links, function, delay)
    
    # 保存结果
    processor.save_results()
    if function == "text":
        processor.save_text_only()
    
    print("\n🎉 批量处理完成！")

if __name__ == "__main__":
    asyncio.run(main())
