#!/usr/bin/env python3
"""
抖音MCP服务器命令行工具
使用方法：
python douyin_cli.py [功能] [抖音链接]

功能选项：
- download: 获取下载链接
- text: 提取文本内容  
- info: 获取视频信息
"""

import os
import sys
import json
import asyncio
from douyin_mcp_server.server import DouyinProcessor

# 设置API密钥
os.environ['DASHSCOPE_API_KEY'] = 'sk-d4d314c9749c474d95efbb2e85b0769b'

class DouyinCLI:
    def __init__(self):
        self.processor = DouyinProcessor(os.getenv('DASHSCOPE_API_KEY'))
    
    def get_download_link(self, share_link):
        """获取下载链接"""
        try:
            print("🔍 正在解析抖音链接...")
            video_info = self.processor.parse_share_url(share_link)
            
            result = {
                "status": "success",
                "video_id": video_info["video_id"],
                "title": video_info["title"],
                "download_url": video_info["url"],
                "description": f"视频标题: {video_info['title']}"
            }
            
            print("✅ 解析成功！")
            print(f"📱 视频ID: {result['video_id']}")
            print(f"📝 标题: {result['title']}")
            print(f"🔗 下载链接: {result['download_url']}")
            
            return result
            
        except Exception as e:
            print(f"❌ 获取下载链接失败: {str(e)}")
            return {"status": "error", "error": str(e)}
    
    async def extract_text(self, share_link):
        """提取文本内容"""
        try:
            print("🔍 正在解析抖音链接...")
            video_info = self.processor.parse_share_url(share_link)
            print(f"✅ 视频解析成功: {video_info['title']}")
            
            print("🎧 正在提取文本内容...")
            text_content = self.processor.extract_text_from_video_url(video_info['url'])
            
            print("✅ 文本提取成功！")
            print(f"📝 提取的文本内容:")
            print("-" * 50)
            print(text_content)
            print("-" * 50)
            
            return text_content
            
        except Exception as e:
            print(f"❌ 提取文本失败: {str(e)}")
            return None
    
    def get_video_info(self, share_link):
        """获取视频信息"""
        try:
            print("🔍 正在解析视频信息...")
            video_info = self.processor.parse_share_url(share_link)
            
            result = {
                "video_id": video_info["video_id"],
                "title": video_info["title"],
                "download_url": video_info["url"],
                "status": "success"
            }
            
            print("✅ 信息获取成功！")
            print(f"📱 视频ID: {result['video_id']}")
            print(f"📝 标题: {result['title']}")
            print(f"🔗 视频URL: {result['download_url'][:50]}...")
            
            return result
            
        except Exception as e:
            print(f"❌ 获取视频信息失败: {str(e)}")
            return {"status": "error", "error": str(e)}

def print_usage():
    """打印使用说明"""
    print("""
🎬 抖音MCP服务器命令行工具

使用方法：
    python douyin_cli.py [功能] [抖音链接]

功能选项：
    download    获取无水印视频下载链接（无需API密钥）
    text        提取视频中的文字内容（需要API密钥）
    info        获取视频基本信息（无需API密钥）

示例：
    python douyin_cli.py download "https://v.douyin.com/iJsLnno/"
    python douyin_cli.py text "https://v.douyin.com/iJsLnno/"
    python douyin_cli.py info "https://v.douyin.com/iJsLnno/"

注意：
    - 提取文本功能需要阿里云百炼API密钥
    - 请确保网络连接正常
    - 仅支持抖音视频链接
""")

async def main():
    if len(sys.argv) != 3:
        print_usage()
        return
    
    function = sys.argv[1].lower()
    share_link = sys.argv[2]
    
    print(f"🚀 启动抖音MCP服务器...")
    print(f"🎯 功能: {function}")
    print(f"🔗 链接: {share_link}")
    print("=" * 60)
    
    cli = DouyinCLI()
    
    if function == "download":
        result = cli.get_download_link(share_link)
    elif function == "text":
        result = await cli.extract_text(share_link)
    elif function == "info":
        result = cli.get_video_info(share_link)
    else:
        print(f"❌ 不支持的功能: {function}")
        print_usage()
        return
    
    print("\n🎉 操作完成！")

if __name__ == "__main__":
    asyncio.run(main())
