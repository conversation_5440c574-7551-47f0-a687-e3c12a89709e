#!/usr/bin/env python3
"""
测试抖音MCP服务器功能
"""

import os
import asyncio
from douyin_mcp_server.server import DouyinProcessor

# 设置API密钥
os.environ['DASHSCOPE_API_KEY'] = 'sk-d4d314c9749c474d95efbb2e85b0769b'

async def test_basic_functionality():
    """测试基本功能"""
    print("🧪 开始测试抖音MCP服务器...")
    
    # 测试用的抖音分享链接（请替换为实际的链接）
    test_share_link = "https://v.douyin.com/xxxxxxx/"  # 请替换为真实的抖音分享链接
    
    try:
        processor = DouyinProcessor(os.getenv('DASHSCOPE_API_KEY'))
        
        print("✅ DouyinProcessor 初始化成功")
        print(f"📱 API密钥已设置: {os.getenv('DASHSCOPE_API_KEY')[:10]}...")
        print(f"🎯 使用模型: {processor.model}")
        
        # 注意：实际测试需要真实的抖音分享链接
        print("\n⚠️  要测试完整功能，请：")
        print("1. 获取一个真实的抖音分享链接")
        print("2. 替换 test_share_link 变量")
        print("3. 重新运行测试")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False

if __name__ == "__main__":
    success = asyncio.run(test_basic_functionality())
    if success:
        print("\n🎉 基础测试通过！程序已准备就绪。")
    else:
        print("\n💥 测试失败，请检查配置。")
