#!/usr/bin/env python3
"""
使用真实抖音链接测试功能
"""

import os
import asyncio
import json
from douyin_mcp_server.server import DouyinProcessor

# 设置API密钥
os.environ['DASHSCOPE_API_KEY'] = 'sk-d4d314c9749c474d95efbb2e85b0769b'

async def test_with_real_link():
    """使用真实链接测试"""
    print("🧪 抖音MCP服务器功能测试")
    print("=" * 50)
    
    # 请在这里输入真实的抖音分享链接
    test_link = input("请输入抖音分享链接: ").strip()
    
    if not test_link:
        print("❌ 未输入链接，退出测试")
        return
    
    try:
        processor = DouyinProcessor(os.getenv('DASHSCOPE_API_KEY'))
        
        print("\n🔍 步骤1: 解析视频信息...")
        video_info = processor.parse_share_url(test_link)
        print(f"✅ 视频ID: {video_info['video_id']}")
        print(f"✅ 标题: {video_info['title']}")
        print(f"✅ 下载链接: {video_info['url'][:50]}...")
        
        print("\n📝 步骤2: 提取文本内容...")
        text_content = processor.extract_text_from_video_url(video_info['url'])
        print(f"✅ 提取的文本: {text_content}")
        
        print("\n🎉 测试完成！")
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")

if __name__ == "__main__":
    asyncio.run(test_with_real_link())
